#!/usr/bin/env python3
"""
Cura Translation Compiler Script
================================

This script compiles .po translation files to .mo binary files for both Cura and Uranium.
It creates the proper directory structure (LC_MESSAGES) required by gettext.

Usage:
    python compile_translations.py

The script will:
1. Find all .po files in Cura/resources/i18n and Uranium/resources/i18n
2. Compile them to .mo files using msgfmt
3. Place them in the correct LC_MESSAGES directory structure
4. Report compilation status for each file

Requirements:
- msgfmt command (part of gettext tools)
- On macOS: brew install gettext
- On Ubuntu/Debian: apt-get install gettext
- On Windows: Install gettext tools or use MSYS2
"""

import os
import sys
import subprocess
from pathlib import Path
import shutil

def check_msgfmt():
    """Check if msgfmt command is available"""
    try:
        result = subprocess.run(['msgfmt', '--version'], 
                              capture_output=True, text=True, check=True)
        print(f"✓ msgfmt found: {result.stdout.split()[0]} {result.stdout.split()[1]}")
        return True
    except (subprocess.CalledProcessError, FileNotFoundError):
        print("✗ msgfmt command not found!")
        print("Please install gettext tools:")
        print("  macOS: brew install gettext")
        print("  Ubuntu/Debian: sudo apt-get install gettext")
        print("  Windows: Install gettext tools or use MSYS2")
        return False

def compile_po_file(po_file_path, output_dir, domain):
    """Compile a single .po file to .mo file"""
    po_path = Path(po_file_path)
    
    # Extract language code from directory name
    lang_code = po_path.parent.name
    
    # Create output directory structure: lang_code/LC_MESSAGES/
    mo_dir = Path(output_dir) / lang_code / "LC_MESSAGES"
    mo_dir.mkdir(parents=True, exist_ok=True)
    
    # Output .mo file path
    mo_file = mo_dir / f"{domain}.mo"
    
    try:
        # Compile .po to .mo using msgfmt
        result = subprocess.run([
            'msgfmt', 
            str(po_path), 
            '-o', str(mo_file),
            '-f'  # Force compilation even with errors
        ], capture_output=True, text=True, check=True)
        
        print(f"✓ Compiled: {po_path.relative_to(Path.cwd())} -> {mo_file.relative_to(Path.cwd())}")
        return True
        
    except subprocess.CalledProcessError as e:
        print(f"✗ Failed to compile {po_path}: {e.stderr}")
        return False

def compile_translations():
    """Main function to compile all translation files"""
    print("Cura Translation Compiler")
    print("=" * 50)
    
    # Check if msgfmt is available
    if not check_msgfmt():
        return False
    
    script_dir = Path(__file__).parent
    success_count = 0
    total_count = 0
    
    # Compile Cura translations
    print("\n📁 Compiling Cura translations...")
    cura_i18n_dir = script_dir / "resources" / "i18n"
    if cura_i18n_dir.exists():
        for po_file in cura_i18n_dir.glob("*/cura.po"):
            total_count += 1
            if compile_po_file(po_file, cura_i18n_dir, "cura"):
                success_count += 1
        
        # Also compile fdmprinter and fdmextruder translations
        for po_file in cura_i18n_dir.glob("*/fdmprinter.def.json.po"):
            total_count += 1
            if compile_po_file(po_file, cura_i18n_dir, "fdmprinter.def.json"):
                success_count += 1
                
        for po_file in cura_i18n_dir.glob("*/fdmextruder.def.json.po"):
            total_count += 1
            if compile_po_file(po_file, cura_i18n_dir, "fdmextruder.def.json"):
                success_count += 1
    else:
        print(f"✗ Cura i18n directory not found: {cura_i18n_dir}")
    
    # Compile Uranium translations
    print("\n📁 Compiling Uranium translations...")
    uranium_i18n_dir = script_dir.parent / "Uranium" / "resources" / "i18n"
    if uranium_i18n_dir.exists():
        for po_file in uranium_i18n_dir.glob("*/uranium.po"):
            total_count += 1
            if compile_po_file(po_file, uranium_i18n_dir, "uranium"):
                success_count += 1
    else:
        print(f"✗ Uranium i18n directory not found: {uranium_i18n_dir}")
    
    # Summary
    print("\n" + "=" * 50)
    print(f"📊 Compilation Summary:")
    print(f"   Total files: {total_count}")
    print(f"   Successful: {success_count}")
    print(f"   Failed: {total_count - success_count}")
    
    if success_count == total_count:
        print("🎉 All translation files compiled successfully!")
        print("\n💡 Tips:")
        print("   - Restart Cura to see the translations")
        print("   - Check Preferences > General > Language settings")
        print("   - Make sure your system locale supports the language")
        return True
    else:
        print("⚠️  Some translation files failed to compile")
        return False

if __name__ == "__main__":
    success = compile_translations()
    sys.exit(0 if success else 1)
