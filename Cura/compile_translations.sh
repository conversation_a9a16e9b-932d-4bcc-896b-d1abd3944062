#!/bin/bash

# Cura Translation Compiler Script
# =================================
# This script compiles .po translation files to .mo binary files for both Cura and Uranium.
# It creates the proper directory structure (LC_MESSAGES) required by gettext.

set -e  # Exit on any error

echo "Cura Translation Compiler"
echo "=================================================="

# Check if msgfmt is available
if ! command -v msgfmt &> /dev/null; then
    echo "✗ msgfmt command not found!"
    echo "Please install gettext tools:"
    echo "  macOS: brew install gettext"
    echo "  Ubuntu/Debian: sudo apt-get install gettext"
    echo "  Windows: Install gettext tools or use MSYS2"
    exit 1
fi

echo "✓ msgfmt found: $(msgfmt --version | head -n1)"

# Get script directory
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
SUCCESS_COUNT=0
TOTAL_COUNT=0

# Function to compile a single .po file
compile_po_file() {
    local po_file="$1"
    local domain="$2"
    local base_dir="$3"
    
    # Extract language code from directory name
    local lang_code=$(basename "$(dirname "$po_file")")
    
    # Create output directory structure: lang_code/LC_MESSAGES/
    local mo_dir="$base_dir/$lang_code/LC_MESSAGES"
    mkdir -p "$mo_dir"
    
    # Output .mo file path
    local mo_file="$mo_dir/$domain.mo"
    
    echo "  Compiling: $po_file -> $mo_file"
    
    if msgfmt "$po_file" -o "$mo_file" -f; then
        echo "  ✓ Success"
        ((SUCCESS_COUNT++))
    else
        echo "  ✗ Failed"
    fi
    ((TOTAL_COUNT++))
}

# Compile Cura translations
echo ""
echo "📁 Compiling Cura translations..."
CURA_I18N_DIR="$SCRIPT_DIR/resources/i18n"

if [ -d "$CURA_I18N_DIR" ]; then
    # Compile cura.po files
    for po_file in "$CURA_I18N_DIR"/*/cura.po; do
        if [ -f "$po_file" ]; then
            compile_po_file "$po_file" "cura" "$CURA_I18N_DIR"
        fi
    done
    
    # Compile fdmprinter.def.json.po files
    for po_file in "$CURA_I18N_DIR"/*/fdmprinter.def.json.po; do
        if [ -f "$po_file" ]; then
            compile_po_file "$po_file" "fdmprinter.def.json" "$CURA_I18N_DIR"
        fi
    done
    
    # Compile fdmextruder.def.json.po files
    for po_file in "$CURA_I18N_DIR"/*/fdmextruder.def.json.po; do
        if [ -f "$po_file" ]; then
            compile_po_file "$po_file" "fdmextruder.def.json" "$CURA_I18N_DIR"
        fi
    done
else
    echo "✗ Cura i18n directory not found: $CURA_I18N_DIR"
fi

# Compile Uranium translations
echo ""
echo "📁 Compiling Uranium translations..."
URANIUM_I18N_DIR="$SCRIPT_DIR/../Uranium/resources/i18n"

if [ -d "$URANIUM_I18N_DIR" ]; then
    for po_file in "$URANIUM_I18N_DIR"/*/uranium.po; do
        if [ -f "$po_file" ]; then
            compile_po_file "$po_file" "uranium" "$URANIUM_I18N_DIR"
        fi
    done
else
    echo "✗ Uranium i18n directory not found: $URANIUM_I18N_DIR"
fi

# Summary
echo ""
echo "=================================================="
echo "📊 Compilation Summary:"
echo "   Total files: $TOTAL_COUNT"
echo "   Successful: $SUCCESS_COUNT"
echo "   Failed: $((TOTAL_COUNT - SUCCESS_COUNT))"

if [ $SUCCESS_COUNT -eq $TOTAL_COUNT ]; then
    echo "🎉 All translation files compiled successfully!"
    echo ""
    echo "💡 Tips:"
    echo "   - Restart Cura to see the translations"
    echo "   - Check Preferences > General > Language settings"
    echo "   - Make sure your system locale supports the language"
    exit 0
else
    echo "⚠️  Some translation files failed to compile"
    exit 1
fi
